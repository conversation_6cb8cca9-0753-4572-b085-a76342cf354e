function debounceFunc(func, delay) {
  let timer = null; // 用于存储定时器ID
  return function (...args) {
    // 如果有未执行的定时器，清除它
    if (timer) {
      clearTimeout(timer);
    }
    // 设置新的定时器，延迟执行回调
    timer = setTimeout(() => {
      func.apply(this, args); // 执行回调，并传递参数
    }, delay);
  };
}

function throttle(func, delay) {
  let lastTime = 0; // 记录上一次执行的时间
  return function (...args) {
    const currentTime = Date.now();
    // 如果当前时间与上一次执行时间的差值大于延迟时间，则执行回调
    if (currentTime - lastTime >= delay) {
      func.apply(this, args); // 执行回调，并传递参数
      lastTime = currentTime; // 更新上一次执行时间
    }
  };
}

// 使用示例
function handleInput(value) {
  console.log("Input value:", value);
}
const debouncedInput = debounceFunc(handleInput, 300);
// 模拟输入事件
debouncedInput("a"); // 不会立即执行
debouncedInput("ab"); // 清除上一次定时器，重新计时
debouncedInput("abc"); // 清除上一次定时器，重新计时

// 使用示例
function handleScroll() {
  console.log("Scroll event triggered");
}
const throttledScroll = throttleFunc(handleScroll, 1000);
// 模拟滚动事件
setInterval(() => {
  throttledScroll();
}, 100); // 每100ms触发一次，但由于节流，只有每1000ms执行一次


