{"name": "umi-demo", "version": "1.0.2", "scripts": {"dev": "PORT=3333 umi dev", "build": "umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "umi dev"}, "dependencies": {"@amazebird/antd-business-field": "^3.5.5", "@amazebird/antd-field": "3.1.1", "@amazebird/antd-schema-form": "3.1.1", "@amazebird/antd-schema-table": "3.1.1", "@amazebird/editor-component": "0.0.2", "@amazebird/schema-form": "^3.1.1", "@amazebird/utils": "^3.3.2", "@babel/runtime-corejs3": "^7.27.1", "@galaxy/upload": "^3.7.3", "@galaxy/upload-component": "^3.4.0", "antd": "4.24.14", "antd-mobile": "^5.39.0", "axios": "^1.7.8", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "moment": "^2.30.1", "react": "18.2.0", "react-dom": "18.2.0", "umi": "^4.0.42"}, "devDependencies": {"@tsconfig/node14": "^1.0.3", "@types/react": "^18.3.4", "@types/react-dom": "^18.3.0", "typescript": "^4.1.2"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "files": ["src/**/*", "tsconfig.json", ".npmrc", ".giti<PERSON>re", "README.md", ".umirc.ts", "package.json", "typings.d.ts"]}