class eventEmitter {
  constructor() {
    this.events = {};
  }

  //订阅
  on(eventName, callback) {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }
    this.events[eventName].push(callback);
  }

  //发布
  emit(eventName, ...args) {
    if (this.events[eventName]) {
      this.events[eventName].forEach((callback) => callback(...args));
    }
  }

  //取消订阅
  off(eventName, callback) {
    if (this.events[eventName]) {
      this.events[eventName] = this.events[eventName].filter(
        (cb) => cb !== callback
      );
    }
  }

  //只订阅一次
  once(eventName, callback) {
    const onceCallback = (...args) => {
      callback(...args);
      this.off(eventName, onceCallback);
    };
    this.on(eventName, onceCallback);
  }
}

const eve = new eventEmitter();

const callback1 = (data) => {
  console.log("received data1", data);
};

const callback2 = (data) => {
  console.log("received data2", data);
};

eve.on("test", callback1);
eve.on("test", callback2);

eve.emit("test", "测试数据");

eve.off("test", callback1);

eve.emit("test", "测试数据again");
