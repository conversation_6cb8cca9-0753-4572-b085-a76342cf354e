import yayJpg from '../assets/yay.jpg';
import { useEffect } from 'react'
import { init as UploadInit } from '@galaxy/upload'

export default function HomePage() {

  useEffect(() => {
    UploadInit({ env: 'itPre' })
  }, [])

  return (
    <div>
      <h2>Yay! Welcome to umi with electron!</h2>
      <p>
        <img src={yayJpg} width="388" />
      </p>
      <p>
        To get started, edit <code>pages/index.tsx</code> and save to reload.
      </p>
      <button
        onClick={async () => {
          window.alert(await window.$api.getPlatform());
          window.alert('edit src/main/ipc/platform.ts and try me again!');
        }}
      >
        what is my platform?
      </button>
    </div>
  );
}
