import React, { useEffect } from 'react'
import { Item, SchemaForm } from '@amazebird/antd-schema-form'
import '@amazebird/antd-field'
import '@amazebird/antd-business-field'
import { message } from 'antd'

interface IProps {

}


const FormDemo: React.FC<IProps> = (props) => {

  const form = SchemaForm.createForm()

  const schema = {
    attachment: {
      component: 'Upload',
      label: '附件',
      visible: true,
      props: {
        oss: {
          service: '/admin/staff_relation',
          module: 'staff_relation',
        },
        clientCode: 'GLOBAL',
        listType: 'text',
        accept:
          'image/jpg,image/jpeg,image/png,application/pdf,application/x-rar-compressed,application/zip',
        fileSizeLimit: 50,
        onChange: (fileList) => {
          if (fileList.length === 10) {
            message.warn('最多支持上传10个文件')
          }
        },
        onPreview: (file) => {
          debugger
          console.log(file, 'file')
        },
        maxNum: 10,
        remark: '支持扩展名：pdf、jpg、jpeg、png、rar、zip，单个文件不超过 50M',
      },
    },
    value: {
      component: "Input.Text",
      label: '员工诉求金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
      mode: 'detail'
    },
    value2: {
      label: '选项',
      component: 'Select',
      options: [
        {
          label: '选项1',
          value: 1
        },
        {
          label: '选项2',
          value: 2
        },
        {
          label: '选项3',
          value: 3
        }
      ],
      props: {
        mode: 'multiple',
      },
      mode: 'detail'
    }
  }

  useEffect(() => {
    form.setFieldsValue({
      value: 300,
      value2: [1, 2],
    })
  }, [])




  return <div>
    <SchemaForm schema={schema} form={form}>
      <Item field="attachment"></Item>
      <Item field="value"></Item>
      <Item field="value2"></Item>
    </SchemaForm>
  </div>
}

export default FormDemo
