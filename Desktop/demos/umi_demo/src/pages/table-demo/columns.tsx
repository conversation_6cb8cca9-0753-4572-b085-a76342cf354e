import { SchemaColumnType } from '@amazebird/antd-schema-table'
import React from 'react'

const createNumberColumn = (title: string, dataIndex: string) => ({
  title,
  dataIndex,
  align: 'right' as const,
  cell: {
    type: 'Number' as const,
    props: { fixed: 2 },
  },
})

export const useColumns: () => SchemaColumnType = () => {
  return [
    {
      title: '人员',
      dataIndex: 'userNum',
      width: 150,
      fixed: 'left',
      cell: ({ _, record }) => {
        return (
          <div>
            <span style={{ marginRight: 4 }}> {record.userName} </span>
            <span> {record.userNum} </span>
          </div>
        )
      },
    },
    {
      title: '一级部门',
      dataIndex: 'firstDepartmentName',
      width: 100,
      cell: { type: 'Text', props: { ellipsis: { direction: 'end' } } },
    },
    {
      title: '二级部门',
      dataIndex: 'secondDepartmentName',
      width: 100,
      cell: { type: 'Text', props: { ellipsis: { direction: 'end' } } },
    },
    {
      title: '三级部门',
      dataIndex: 'thirdDepartmentName',
      width: 100,
      cell: { type: 'Text', props: { ellipsis: { direction: 'end' } } },
    },
    {
      title: '四级部门',
      dataIndex: 'fourthDepartmentName',
      width: 100,
      cell: { type: 'Text', props: { ellipsis: { direction: 'end' } } },
    },
    {
      title: '部门',
      dataIndex: 'nodeName',
      width: 100,
      cell: { type: 'Text', props: { ellipsis: { direction: 'end' } } },
    },
    {
      title: '岗位',
      dataIndex: 'positionName',
      width: 120,
      cell: { type: 'Text', props: { ellipsis: { direction: 'end' } } },
    },
    {
      title: '考勤年月',
      dataIndex: 'auditMonth',
      width: 70,
      cell: { type: 'Date', props: { format: 'YYYYMM' } },
    },
    {
      title: '入职日期',
      dataIndex: 'timeEntry',
      width: 100,
      cell: { type: 'Date', props: { format: 'YYYY-MM-DD' } },
    },
    {
      title: '离职日期',
      dataIndex: 'timeLeave',
      width: 100,
      cell: { type: 'Date', props: { format: 'YYYY-MM-DD' } },
    },
    {
      title: '人员状态',
      dataIndex: 'isEnabled',
      width: 100,
      cell: ({ _, record }) => {
        return <div>{record.isEnabled ? '在职' : '离职'} </div>
      },
    },
    {
      title: '异常日期',
      dataIndex: 'beginDate',
      width: 100,
      cell: { type: 'Date', props: { format: 'YYYY-MM-DD' } },
    },
    {
      title: '时段',
      dataIndex: 'sequence',
      width: 100,
    },
    {
      title: '排班门店',
      dataIndex: 'storeAbbrName',
      width: 100,
    },
    createNumberColumn('夜班时数', 'nightlyHours'),
    createNumberColumn('差异天数', 'differenceValue'),
    {
      title: '月应出勤天数（日结果合计）',
      dataIndex: 'shouldAttendDaysSum',
      align: 'right' as const,
      width: 180,
      cell: {
        type: 'Number' as const,
        props: { fixed: 2 },
      },
    },
    createNumberColumn('班次时长', 'arrangeTotalHours'),
    createNumberColumn('应出勤时数', 'shouldAttendHours'),
    createNumberColumn('应出勤天数', 'shouldAttendDays'),
    createNumberColumn('实出勤时数', 'actualAttendHours'),
    createNumberColumn('实出勤天数', 'actualAttendDays'),
    createNumberColumn('旷工时数', 'absentHours'),
    createNumberColumn('旷工天数', 'absentDays'),
    createNumberColumn('请假时数', 'totalLeaveHours'),
    createNumberColumn('请假天数', 'totalLeaveDays'),
    {
      title: '确认人',
      dataIndex: 'userNumConfirm',
      width: 150,
      cell: ({ _, record }) => {
        return (
          <div>
            <span style={{ marginRight: 4 }}> {record.userNameConfirm} </span>
            <span> {record.userNumConfirm} </span>
          </div>
        )
      },
    },
    {
      title: '异常备注',
      dataIndex: 'confirmRemark',
    },
    {
      title: '确认时间',
      dataIndex: 'confirmTime',
      width: 150,
      cell: { type: 'Date', props: { format: 'YYYY-MM-DD HH:mm' } },
    },
    {
      title: '创建时间',
      dataIndex: 'timeCreate',
      width: 150,
      cell: { type: 'Date', props: { format: 'YYYY-MM-DD HH:mm' } },
    },
    {
      title: '更新时间',
      dataIndex: 'timeUpdate',
      width: 150,
      cell: { type: 'Date', props: { format: 'YYYY-MM-DD HH:mm' } },
    },
  ]
}
