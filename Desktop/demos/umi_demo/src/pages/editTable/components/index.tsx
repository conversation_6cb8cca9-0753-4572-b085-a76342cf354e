import React from 'react';
import { Form, Table, Select, Button } from 'antd';

const { Option } = Select;

const EditableTable = () => {
  const [form] = Form.useForm();

  // 固定选项列表
  const options = [
    { value: 'option1', label: '选项1' },
    { value: 'option2', label: '选项2' },
    { value: 'option3', label: '选项3' },
    { value: 'option4', label: '选项4' },
  ];

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      render: (text, record, index) => index + 1,
    },
    {
      title: '选择项',
      dataIndex: 'selectValue',
      render: (text, field) => (
        <Form.Item
          name={[field.name, 'selectValue']}
          rules={[{ required: true, message: '请选择选项' }]}
        >
          <Select placeholder="请选择">
            {options.map((opt) => {
              // 获取所有行的值
              const values = form.getFieldValue('items') || [];
              // 收集其他行的已选值（排除当前行）
              const selectedValues = values
                .map((v, i) => (i !== field.fieldKey ? v?.selectValue : undefined))
                .filter(Boolean);
              // 如果已被其他行选中，则禁用
              const isDisabled = selectedValues.includes(opt.value);
              return (
                <Option key={opt.value} value={opt.value} disabled={isDisabled}>
                  {opt.label}
                </Option>
              );
            })}
          </Select>
        </Form.Item>
      ),
    },
    {
      title: '操作',
      render: (text, field) => (
        <Button type="link" onClick={() => remove(field.name)}>
          删除
        </Button>
      ),
    },
  ];

  let remove; // 用于删除行的函数

  return (
    <Form form={form} initialValues={{ items: [{ selectValue: null }] }}>
      <Form.List name="items">
        {(fields, { add, remove: rm }) => {
          remove = rm; // 赋值 remove 函数
          return (
            <>
              <Table
                columns={columns}
                dataSource={fields}
                rowKey={(record) => record.key}
                pagination={false}
              />
              <Button type="dashed" onClick={() => add({ selectValue: null })} style={{ marginTop: 16 }}>
                添加行
              </Button>
            </>
          );
        }}
      </Form.List>
    </Form>
  );
};

export default EditableTable;