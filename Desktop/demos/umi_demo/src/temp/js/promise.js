class MyPromise {
  constructor(executor) {
    this.state = 'pending';
    this.value = null;
    this.callbacks = [];
    const resolve = (value) => {
      if (this.state === 'pending') {
        this.state = 'fulfilled';
        this.value = value;
        this.callbacks.forEach((cb) => cb.onFulfilled(value));
      }
    };
    const reject = (reason) => {
      if (this.state === 'pending') {
        this.state = 'rejected';
        this.value = reason;
        this.callbacks.forEach((cb) => cb.onRejected(reason));
      }
    };
    try {
      executor(resolve, reject);
    } catch (err) {
      reject(err);
    }
  }
  then(onFulfilled, onRejected) {
    return new MyPromise((resolve, reject) => {
      const callback = {
        onFulfilled: (value) => {
          try {
            const result = onFulfilled ? onFulfilled(value) : value;
            resolve(result);
          } catch (err) {
            reject(err);
          }
        },
        onRejected: (reason) => {
          try {
            const result = onRejected ? onRejected(reason) : reason;
            resolve(result);
          } catch (err) {
            reject(err);
          }
        },
      };
      if (this.state === 'pending') {
        this.callbacks.push(callback);
      } else if (this.state === 'fulfilled') {
        setTimeout(() => callback.onFulfilled(this.value), 0);
      } else {
        setTimeout(() => callback.onRejected(this.value), 0);
      }
    });
  }
}