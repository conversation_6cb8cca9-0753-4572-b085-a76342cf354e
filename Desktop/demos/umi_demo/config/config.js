const DependencyAnalysisPlugin = require("./DependencyAnalysisPlugin");

export default {
  configureWebpack(config) {
    console.log("[Umi Config] configureWebpack is loaded");
    const DependencyAnalysisPlugin = require("./DependencyAnalysisPlugin");
    config.plugins.push(
      new DependencyAnalysisPlugin({
        entry: "src",
        include: ".js|.jsx|.ts|.tsx",
        exclude: ["src/assets", "src/utils"],
        isDelete: false,
      })
    );
  },
};
