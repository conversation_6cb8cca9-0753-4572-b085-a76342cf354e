import React from 'react'
import { SchemaTable } from '@amazebird/antd-schema-table'
import { useColumns } from './columns'
import moment from 'moment'

interface DataType {
  key: string
  userNum: string
  userName: string
  firstDepartmentName: string
  secondDepartmentName: string
  thirdDepartmentName: string
  fourthDepartmentName: string
  nodeName: string
  positionName: string
  auditMonth: string
  timeEntry: string
  timeLeave: string
  isEnabled: boolean
  beginDate: string
  sequence: string
  storeAbbrName: string
  nightlyHours: number
  differenceValue: number
  shouldAttendDaysSum: number
  arrangeTotalHours: number
  shouldAttendHours: number
  shouldAttendDays: number
  actualAttendHours: number
  actualAttendDays: number
  absentHours: number
  absentDays: number
  totalLeaveHours: number
  totalLeaveDays: number
  userNumConfirm: string
  userNameConfirm: string
  confirmRemark: string
  confirmTime: number
  timeCreate: number
  timeUpdate: number
}

const columns = useColumns()


// 生成mock数据的工具函数
const generateMockData = (): DataType[] => {
  const surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗']
  const names = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞']
  const firstDepartments = ['总部总部总部总部总部总部', '华北大区总部总部总部', '华东大区总部总部总部', '华南大区总部总部总部', '西北大区总部总部总部', '西南大区总部总部总部总部', '东北大区总部总部总部', '华中大区总部总部总部']
  const secondDepartments = ['运营中心总部总部总部总部', '技术中心总部总部', '财务中心总部总部总部总部', '人力资源中心总部总部总部', '市场中心总部总部总部', '客服中心总部总部总部', '物流中心总部总部总部', '采购中心总部总部总部']
  const thirdDepartments = ['开发部总部总部总部', '测试部总部总部总部', '产品部总部总部总部', '设计部总部总部', '运维部总部总部', '数据部总部总部', '安全部总部总部', '质量部总部总部']
  const fourthDepartments = ['前端组总部总部', '后端组总部', '移动端组总部', '算法组总部', '架构组总部', '运营组总部', '推广组总部', '客户组总部']
  const positions = ['工程师', '高级工程师', '资深工程师', '技术专家', '架构师', '项目经理', '产品经理', '设计师', '运营专员', '分析师']
  const stores = ['北京朝阳店', '上海徐汇店', '广州天河店', '深圳南山店', '杭州西湖店', '成都锦江店', '武汉江汉店', '西安雁塔店']
  const sequences = ['早班', '中班', '晚班', '夜班', '全天班']
  const remarkReasons = [
    '设备故障导致打卡异常',
    '出差期间考勤异常',
    '病假期间补卡',
    '加班时间超时',
    '外出办事忘记打卡',
    '系统维护期间异常',
    '培训期间考勤调整',
    '客户拜访打卡异常',
    '会议冲突导致迟到',
    '交通拥堵影响考勤'
  ]

  const data: DataType[] = []

  for (let i = 1; i <= 200; i++) {
    const surname = surnames[Math.floor(Math.random() * surnames.length)]
    const name = names[Math.floor(Math.random() * names.length)]
    const fullName = surname + name
    const isEnabled = Math.random() > 0.2 // 80%在职

    // 生成随机日期
    const entryYear = 2020 + Math.floor(Math.random() * 4) // 2020-2023年入职
    const entryMonth = Math.floor(Math.random() * 12)
    const entryDay = Math.floor(Math.random() * 28) + 1
    const entryDate = new Date(entryYear, entryMonth, entryDay)

    // 离职日期（仅对离职员工）
    const leaveDate = isEnabled ? null : new Date(2023 + Math.random(), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1)

    // 异常日期（2024年内）
    const beginDate = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1)

    // 确认时间、创建时间、更新时间（使用时间戳）
    const createTime = moment().subtract(Math.random() * 7, 'days').valueOf() // 一周前创建
    const confirmTime = moment(createTime).add(Math.random() * 24, 'hours').valueOf() // 创建后同一天内确认
    const updateTime = moment(createTime).add(Math.random() * 30, 'days').valueOf() // 创建后30天内更新

    // 确认人信息
    const confirmSurname = surnames[Math.floor(Math.random() * surnames.length)]
    const confirmName = names[Math.floor(Math.random() * names.length)]
    const confirmFullName = confirmSurname + confirmName
    const confirmUserNum = `EMP${Math.floor(Math.random() * 100 + 1).toString().padStart(4, '0')}`

    // 生成考勤相关的数值数据
    const shouldAttendDays = Math.round((20 + Math.random() * 5) * 100) / 100 // 应出勤天数 20-25天
    const shouldAttendHours = Math.round(shouldAttendDays * 8 * 100) / 100 // 应出勤时数
    const actualAttendDays = Math.round((shouldAttendDays - Math.random() * 3) * 100) / 100 // 实际出勤天数
    const actualAttendHours = Math.round(actualAttendDays * 8 * 100) / 100 // 实际出勤时数
    const absentDays = Math.round((shouldAttendDays - actualAttendDays) * 100) / 100 // 旷工天数
    const absentHours = Math.round(absentDays * 8 * 100) / 100 // 旷工时数
    const totalLeaveDays = Math.round(Math.random() * 3 * 100) / 100 // 请假天数
    const totalLeaveHours = Math.round(totalLeaveDays * 8 * 100) / 100 // 请假时数

    data.push({
      key: i.toString(),
      userNum: `EMP${i.toString().padStart(4, '0')}`,
      userName: fullName,
      firstDepartmentName: firstDepartments[Math.floor(Math.random() * firstDepartments.length)],
      secondDepartmentName: secondDepartments[Math.floor(Math.random() * secondDepartments.length)],
      thirdDepartmentName: thirdDepartments[Math.floor(Math.random() * thirdDepartments.length)],
      fourthDepartmentName: fourthDepartments[Math.floor(Math.random() * fourthDepartments.length)],
      nodeName: thirdDepartments[Math.floor(Math.random() * thirdDepartments.length)],
      positionName: positions[Math.floor(Math.random() * positions.length)],
      auditMonth: `2024${(Math.floor(Math.random() * 12) + 1).toString().padStart(2, '0')}`,
      timeEntry: entryDate.toISOString().split('T')[0],
      timeLeave: leaveDate ? leaveDate.toISOString().split('T')[0] : '',
      isEnabled: isEnabled,
      beginDate: beginDate.toISOString().split('T')[0],
      sequence: sequences[Math.floor(Math.random() * sequences.length)],
      storeAbbrName: stores[Math.floor(Math.random() * stores.length)],
      nightlyHours: Math.round(Math.random() * 12 * 100) / 100,
      differenceValue: Math.round((Math.random() * 10 - 5) * 100) / 100, // -5到5之间的差异天数
      shouldAttendDaysSum: Math.round((shouldAttendDays + Math.random() * 2) * 100) / 100,
      arrangeTotalHours: Math.round((160 + Math.random() * 40) * 100) / 100,
      shouldAttendHours: shouldAttendHours,
      shouldAttendDays: shouldAttendDays,
      actualAttendHours: actualAttendHours,
      actualAttendDays: actualAttendDays,
      absentHours: absentHours,
      absentDays: absentDays,
      totalLeaveHours: totalLeaveHours,
      totalLeaveDays: totalLeaveDays,
      userNumConfirm: confirmUserNum,
      userNameConfirm: confirmFullName,
      confirmRemark: remarkReasons[Math.floor(Math.random() * remarkReasons.length)],
      confirmTime: confirmTime,
      timeCreate: createTime,
      timeUpdate: updateTime,
    })
  }

  return data
}

const data: DataType[] = generateMockData()

// 调试：输出第一条数据看看时间字段
console.log('Mock数据示例:', data[0])

const TableDemo: React.FC = () => {
  const search = (params: any) => {
    return new Promise<{ data: DataType[]; total: number }>((resolve) => {
      setTimeout(() => {
        resolve({
          data: data,
          total: data.length,
        });
      }, 500);
    });
  }

  return (
    <div>
      <SchemaTable
        columns={columns}
        request={search}

      />
    </div>
  )
}

export default TableDemo
